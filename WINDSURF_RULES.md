# AI Room Redesign - Project Guidelines

## Project Overview
- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS with Radix UI components
- **Database**: Drizzle ORM with Neon
- **Authentication**: Clerk
- **Payments**: PayPal integration

## Code Style
- Use JavaScript (ES6+)
- Single quotes for strings
- Semicolons at the end of statements
- Maximum line length: 100 characters
- 2 spaces for indentation (no tabs)

## Project Structure
```
/app
  /api           # API routes
  /dashboard     # Protected routes
  /_context      # React Context providers
  /components    # Reusable components
  /lib           # Utility functions
  /styles        # Global styles
```

## React & Next.js
- Use functional components with hooks
- Prefer server components by default
- Use the `Image` component for images
- Implement proper error boundaries
- Use route handlers for API endpoints

## Styling Guidelines
- Use Tailwind utility classes primarily
- Create component variants using `class-variance-authority`
- Use CSS modules for complex component styles
- Follow mobile-first responsive design
- Dark mode support using `class` strategy

## State Management
- Use React Context for global state
- Keep state as local as possible
- Prefer server components for data fetching
- Use proper loading and error states

## Database (Drizzle ORM)
- Keep schema definitions in `/db/schema.js`
- Use migrations for schema changes
- Implement proper type safety
- Use transactions for multiple operations

## Authentication (Clerk)
- Protect routes using Clerk middleware
- Use server components for auth checks
- Implement proper role-based access
- Handle auth callbacks properly

## API Development
- Use route handlers in `app/api`
- Implement proper error handling
- Add input validation
- Use proper HTTP status codes
- Implement rate limiting

## Security Best Practices
- Never expose API keys in client code
- Validate all user inputs
- Use environment variables for secrets
- Implement proper CORS policies
- Use Content Security Policy (CSP)

## Testing
- Write unit tests for utility functions
- Write integration tests for components
- Test critical user flows
- Aim for 80%+ test coverage
- Use React Testing Library

## Performance
- Implement code splitting
- Use dynamic imports for large components
- Optimize images
- Lazy load below-the-fold content
- Minimize bundle size

## Git Workflow
- Use semantic commit messages
- Create feature branches from `main`
- Open pull requests for code review
- Require at least one reviewer
- Keep PRs focused and small

## Documentation
- Maintain a clear README
- Document environment variables
- Add JSDoc comments for functions
- Keep CHANGELOG.md updated
- Document component props and usage

## Development Setup
### VS Code Extensions
- ESLint
- Prettier
- Tailwind CSS IntelliSense
- Radix UI
- JavaScript (ES6) code snippets

### Editor Settings
- Format on save
- Auto-fix on save
- Use project's Prettier config

## Deployment
- Use environment variables for configuration
- Set up proper CI/CD pipeline
- Implement proper logging
- Monitor performance and errors
- Set up proper caching headers

## Code Review Guidelines
- Check for security vulnerabilities
- Ensure proper error handling
- Verify responsive design
- Check for accessibility issues
- Ensure code follows these guidelines

---
*Last updated: May 2025*
