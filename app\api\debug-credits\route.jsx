import { db } from "@/config/db";
import { Users } from "@/config/schema";
import { NextResponse } from "next/server";
import { eq } from "drizzle-orm";

export async function GET(req) {
    try {
        // Get all users to see the credit distribution
        const allUsers = await db.select().from(Users);
        
        console.log('All users in database:', allUsers);
        
        return NextResponse.json({
            success: true,
            users: allUsers,
            totalUsers: allUsers.length,
            message: 'Credit debug information retrieved successfully'
        });
    } catch (error) {
        console.error('Error in debug-credits:', error);
        return NextResponse.json({
            success: false,
            error: error.message || 'Internal server error'
        }, { status: 500 });
    }
}

export async function POST(req) {
    try {
        const { action, userEmail, credits } = await req.json();
        
        if (action === 'reset') {
            // Reset all users to 3 credits for testing
            const result = await db.update(Users).set({
                credits: 3
            });
            
            return NextResponse.json({
                success: true,
                message: 'All user credits reset to 3'
            });
        }
        
        if (action === 'set' && userEmail && credits !== undefined) {
            // Set specific user credits
            const result = await db.update(Users).set({
                credits: credits
            }).where(eq(Users.email, userEmail));

            return NextResponse.json({
                success: true,
                message: `Credits set to ${credits} for ${userEmail}`
            });
        }

        if (action === 'cleanup-duplicates') {
            // Find and remove duplicate users, keeping the first one
            const allUsers = await db.select().from(Users);
            const emailMap = new Map();
            const duplicatesToDelete = [];

            // Group users by email and identify duplicates
            allUsers.forEach(user => {
                if (emailMap.has(user.email)) {
                    // This is a duplicate, mark for deletion
                    duplicatesToDelete.push(user.id);
                    console.log(`Found duplicate user: ${user.email} (ID: ${user.id})`);
                } else {
                    // First occurrence, keep it
                    emailMap.set(user.email, user);
                }
            });

            if (duplicatesToDelete.length > 0) {
                // Delete duplicate users
                for (const userId of duplicatesToDelete) {
                    await db.delete(Users).where(eq(Users.id, userId));
                }

                return NextResponse.json({
                    success: true,
                    message: `Removed ${duplicatesToDelete.length} duplicate users`,
                    deletedIds: duplicatesToDelete
                });
            } else {
                return NextResponse.json({
                    success: true,
                    message: 'No duplicate users found'
                });
            }
        }

        return NextResponse.json({
            success: false,
            error: 'Invalid action or missing parameters'
        }, { status: 400 });
        
    } catch (error) {
        console.error('Error in debug-credits POST:', error);
        return NextResponse.json({
            success: false,
            error: error.message || 'Internal server error'
        }, { status: 500 });
    }
}
