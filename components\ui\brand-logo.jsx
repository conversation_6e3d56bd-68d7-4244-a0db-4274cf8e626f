"use client"

import React, { useState, useEffect } from 'react'
import { Home } from 'lucide-react'
import { cn } from "@/lib/utils"

const BrandLogo = ({ 
  size = "default", 
  showIcon = true, 
  animated = false,
  colorVariant = "default",
  className = ""
}) => {
  const [currentVariant, setCurrentVariant] = useState(0)

  // Color combinations for the branding
  const colorVariants = [
    // Variant 1: Blue-Purple Gradient (Default)
    {
      name: "default",
      icon: "text-primary",
      room: "text-slate-700 dark:text-slate-300",
      designs: "text-emerald-600 dark:text-emerald-400",
      ai: "text-accent",
      gradient: "bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent"
    },
    // Variant 2: Purple-Pink Gradient
    {
      name: "purple-pink",
      icon: "text-purple-600",
      room: "text-gray-700 dark:text-gray-300",
      designs: "text-orange-600 dark:text-orange-400",
      ai: "text-purple-500",
      gradient: "bg-gradient-to-r from-purple-600 via-pink-500 to-purple-600 bg-clip-text text-transparent"
    },
    // Variant 3: <PERSON>-<PERSON><PERSON>
    {
      name: "blue-cyan",
      icon: "text-blue-600",
      room: "text-gray-700 dark:text-gray-300",
      designs: "text-rose-600 dark:text-rose-400",
      ai: "text-blue-500",
      gradient: "bg-gradient-to-r from-blue-600 via-cyan-400 to-blue-600 bg-clip-text text-transparent"
    }
  ]

  // Auto-cycle through variants if animated
  useEffect(() => {
    if (animated) {
      const interval = setInterval(() => {
        setCurrentVariant((prev) => (prev + 1) % colorVariants.length)
      }, 3000) // Change every 3 seconds
      
      return () => clearInterval(interval)
    }
  }, [animated, colorVariants.length])

  // Get current color variant
  const variant = colorVariant === "default" 
    ? colorVariants[currentVariant] 
    : colorVariants.find(v => v.name === colorVariant) || colorVariants[0]

  // Size configurations
  const sizeConfig = {
    small: {
      icon: "h-4 w-4",
      text: "text-base",
      spacing: "space-x-2"
    },
    default: {
      icon: "h-6 w-6",
      text: "text-xl",
      spacing: "space-x-2"
    },
    large: {
      icon: "h-7 w-7",
      text: "text-2xl lg:text-3xl",
      spacing: "space-x-3"
    }
  }

  const config = sizeConfig[size] || sizeConfig.default

  return (
    <div className={cn("flex items-center", config.spacing, className)}>
      {showIcon && (
        <div className="relative">
          <Home className={cn(config.icon, variant.icon, "drop-shadow-lg transition-colors duration-500")} />
          <div className="absolute -top-1 -right-1 w-3.5 h-3.5 bg-accent rounded-full animate-pulse"></div>
        </div>
      )}
      
      {animated ? (
        <span className={cn(config.text, "transition-all duration-500", variant.gradient)}>
          <span className="font-light tracking-wide italic">Room</span>
          <span className="font-semibold tracking-normal">Designs</span>
          <span className="font-extrabold tracking-tight">AI</span>
        </span>
      ) : (
        <span className={cn(config.text)}>
          <span className={cn("font-light tracking-wide italic transition-colors duration-300", variant.room)}>Room</span>
          <span className={cn("font-semibold tracking-normal transition-colors duration-300", variant.designs)}>Designs</span>
          <span className={cn("font-extrabold tracking-tight transition-colors duration-300", variant.ai)}>AI</span>
        </span>
      )}
    </div>
  )
}

export default BrandLogo
