@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

.handwriting {
  font-family: 'Kalam', 'Comic Sans MS', cursive;
}

.before-after-slider img{
  height: 200px !important;
  object-fit: cover !important;
}

.before-after-slider.large img {
  height: 400px !important;
  object-fit: cover !important;
  max-height: 70vh !important;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222 84% 4.9%;
    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222 47% 11%;
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;
    --accent: 262 83% 58%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 221 83% 53%;
    --chart-1: 221 83% 53%;
    --chart-2: 262 83% 58%;
    --chart-3: 200 98% 39%;
    --chart-4: 280 100% 70%;
    --chart-5: 340 82% 52%;
    --radius: 0.75rem;
  }
  .dark {
    --background: 222 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217 91% 60%;
    --primary-foreground: 222 84% 4.9%;
    --secondary: 217 32% 17%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217 32% 17%;
    --muted-foreground: 215 20% 65%;
    --accent: 270 95% 75%;
    --accent-foreground: 222 84% 4.9%;
    --destructive: 0 62% 30%;
    --destructive-foreground: 210 40% 98%;
    --border: 217 32% 17%;
    --input: 217 32% 17%;
    --ring: 217 91% 60%;
    --chart-1: 217 91% 60%;
    --chart-2: 270 95% 75%;
    --chart-3: 200 98% 39%;
    --chart-4: 280 100% 70%;
    --chart-5: 340 82% 52%;
  }

}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

