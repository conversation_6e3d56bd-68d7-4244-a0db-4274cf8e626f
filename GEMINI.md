# AI Room Redesign

This is a Next.js application that allows users to redesign their rooms using AI. Users can upload a photo of their room, select a style, and get AI-generated design options.

## Core Development Principles & AI Directives

These principles are paramount. All new features, modifications, and code generations must strictly adhere to them.

### 1. Functional Stability & Non-Disturbance

- **Prioritize existing functionality:** Any change must *not* break or negatively impact already working features.
- **Modularity:** Ensure new code is isolated, focused, and loosely coupled with existing systems. Prefer adding new files/components over heavily modifying existing ones if it helps maintain clarity and prevent regressions.
- **Clear Separation of Concerns:** Logic should be clearly separated (e.g., UI, data fetching, business logic, API handling).
- **Idempotency:** Operations should ideally be idempotent where applicable (e.g., API calls that can be safely retried).

### 2. Security Considerations

- **Input Validation:** All user inputs (client-side and especially server-side in API routes) must be rigorously validated and sanitized to prevent common vulnerabilities (e.g., XSS, SQL Injection).
- **Authentication & Authorization:** Strictly adhere to Clerk's best practices for user authentication. Ensure proper authorization checks are in place for all protected routes and API endpoints, verifying user permissions before allowing access to resources or sensitive actions.
- **Sensitive Data Handling:** Never hardcode sensitive information (API keys, database credentials) directly in the code. Always use environment variables (`.env.local`). Avoid exposing sensitive server-side information to the client.
- **API Security:** Implement rate limiting, proper error handling (avoiding verbose error messages that leak system info), and secure communication protocols (HTTPS).

### 3. Quality Assurance & Bug Prevention

- **Robustness:** Write resilient code that handles expected and unexpected edge cases gracefully.
- **Comprehensive Error Handling:** All asynchronous operations and API interactions (both client and server) must be wrapped in `try...catch` blocks. Provide clear, actionable error messages for logging and user feedback.
- **Maintainability & Readability:** Code should be easy to understand, debug, and maintain by other developers. Follow consistent patterns.
- **Testability:** Structure code in a way that facilitates future unit, integration, and end-to-end testing (even if tests are not immediately generated).
- **Self-Correction & Review:** Before proposing any changes, thoroughly self-evaluate for potential technical or functional bugs based on the above principles.

---

## Tech Stack

- **Framework:** Next.js
- **Authentication:** Clerk
- **Database:** Neon (PostgreSQL) with Drizzle ORM
- **Styling:** Tailwind CSS with shadcn/ui components
- **AI Model:** Replicate
- **Deployment:** Vercel

## Getting Started

1.  **Install dependencies:**
    ```bash
    npm install
    ```
2.  **Set up environment variables:**
    Create a `.env.local` file by copying the `.env.example` file and fill in the required environment variables.
3.  **Run the development server:**
    ```bash
    npm run dev
    ```
    Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Key Features

- User authentication with Clerk
- AI-powered room redesign using the Replicate API
- View and manage redesigned rooms in a dashboard
- Purchase credits to redesign more rooms
- Dark and light mode support

## Project Structure

The project follows the Next.js App Router structure.

-   `app/`: This is the core of the application.
    -   `layout.js`: The root layout for the entire application.
    -   `page.js`: The main landing page.
    -   `(auth)`: A route group for authentication-related pages like sign-in and sign-up, using Clerk.
    -   `dashboard/`: A protected route group for the user dashboard.
        -   `layout.jsx`: A specific layout for the dashboard section.
        -   `page.jsx`: The main dashboard page.
        -   `_components/`: Private components specific to the dashboard routes, co-located with the routes that use them. (Prefixing with `_` ensures these folders are not treated as routes by Next.js.)
    -   `api/`: Contains API routes for server-side logic, such as handling form submissions or communicating with the AI model.
    -   Other top-level folders like `about/`, `contact/`, and `privacy/` define static pages.

-   `components/`: Contains globally reusable React components.
    -   `ui/`: Smaller, general-purpose UI components, many of which are from `shadcn/ui`.

-   `config/`: Holds configuration files.
    -   `db.js`: Database connection setup.
    -   `firebaseConfig.js`: Firebase configuration.
    -   `schema.js`: Drizzle ORM database schema definition.

-   `lib/`: Contains utility functions and helper scripts.
    -   `utils.js`: General utility functions.

-   `public/`: Stores all static assets like images, logos, and fonts that are served directly.

## Conventions

### Language

-   The project is primarily written in **JavaScript** using **React** and the **Next.js** framework.
-   **JSX** is used for component syntax.
-   The codebase utilizes modern JavaScript features (ES6+), including `async/await` and arrow functions.
-   For new complex features, **TypeScript** is the preferred language to enhance type safety and maintainability.

### Formatting

-   Code is formatted with an indent size of **2 spaces**.
-   Follows standard **Prettier** conventions for code style.

### Naming

-   **Components**: `PascalCase` (e.g., `Header`, `RoomDesignCard`).
-   **Files**: Component files are named in `PascalCase` (e.g., `Header.jsx`), while other files (like route segments or utility files) typically use `kebab-case` (e.g., `brand-logo.jsx`).
-   **Variables and Functions**: `camelCase` (e.g., `userDetail`, `setUserDetail`).

### Imports

-   **Absolute imports** are configured with a `@/*` alias, pointing to the root directory.
-   Imports are organized with external libraries listed before internal modules and components.

### Error Handling

-   In API routes, server-side logic is wrapped in `try...catch` blocks to handle potential errors gracefully.
-   Errors are returned as a JSON response with a descriptive error message.

### Styling

-   The project uses **Tailwind CSS** for styling, with components from the **shadcn/ui** library.
-   Utility classes are used directly in the JSX for styling components.
-   **Font Consistency:** Ensure consistent font usage as defined in `tailwind.config.js` or global CSS files (e.g., `globals.css`).

### Next.js Specifics

-   Use Server Components by default where applicable. Mark Client Components with `"use client"`.
-   Keep API routes (`app/api/`) clean and focused on their specific task.
-   **Routing:** App Router route groups (e.g., `(auth)`) are used for organization without affecting URL paths, and `layout.js` / `page.js` define route segments.
-   **Data Fetching:** Prefer native `fetch` with Next.js caching mechanisms in Server Components.

---


   **This file serves as a guide for the AI. Do not modify this section unless explicitly instructed.**