"use client"

import React from 'react'
import Brand<PERSON><PERSON> from './brand-logo'
import { Card, CardContent } from './card'

const BrandShowcase = () => {
  const variants = [
    {
      name: "Primary Theme",
      variant: "default",
      description: "Professional blue-purple gradient"
    },
    {
      name: "Creative Theme", 
      variant: "purple-pink",
      description: "Vibrant purple-pink gradient"
    },
    {
      name: "Tech Theme",
      variant: "blue-cyan", 
      description: "Modern blue-cyan gradient"
    }
  ]

  return (
    <div className="py-16 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-foreground mb-4">
            Brand Identity Variations
          </h2>
          <p className="text-lg text-muted-foreground">
            Our dynamic branding adapts to different contexts and moods
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {variants.map((variant, index) => (
            <Card key={index} className="p-6 hover:shadow-lg transition-shadow duration-300">
              <CardContent className="text-center space-y-4">
                <BrandLogo 
                  size="default"
                  colorVariant={variant.variant}
                  className="justify-center"
                />
                <div>
                  <h3 className="font-semibold text-lg text-foreground">
                    {variant.name}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {variant.description}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div className="mt-12 text-center">
          <div className="inline-block p-8 bg-card rounded-2xl shadow-lg">
            <h3 className="text-xl font-semibold mb-4 text-foreground">
              Animated Version
            </h3>
            <BrandLogo 
              size="large"
              animated={true}
              className="justify-center"
            />
            <p className="text-sm text-muted-foreground mt-4">
              Cycles through all color variations automatically
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BrandShowcase
