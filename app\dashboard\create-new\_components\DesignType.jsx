import React, { useState, useImperative<PERSON>andle, forwardRef, useEffect } from 'react'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { cn } from "@/lib/utils"
import { AlertCircle } from "lucide-react"

const DesignType = forwardRef(function DesignType({ selectedDesignType, onValidationChange }, ref) {
    const [value, setValue] = useState("Modern")
    const [error, setError] = useState("")
    const [touched, setTouched] = useState(false)

    // Set default value on component mount
    useEffect(() => {
        selectedDesignType("Modern")
        validateSelection("Modern")
    }, [])

    // Expose validation method to parent component
    useImperativeHandle(ref, () => ({
        validate: () => {
            const isValid = validateSelection(value)
            setTouched(true)
            return isValid
        },
        reset: () => {
            setValue("")
            setError("")
            setTouched(false)
        },
        getValue: () => value
    }))

    const validateSelection = (currentValue) => {
        if (!currentValue || currentValue.trim() === "") {
            setError("Please select a design type")
            onValidationChange?.(false)
            return false
        }
        setError("")
        onValidationChange?.(true)
        return true
    }

    const handleValueChange = (newValue) => {
        setValue(newValue)
        setTouched(true)
        validateSelection(newValue)
        selectedDesignType(newValue)
    }

    const handleOpenChange = (open) => {
        // Only show error if user has interacted and closed without selecting
        if (!open && touched && !value) {
            setError("Please select a design type")
            onValidationChange?.(false)
        }
    }

    const hasError = error && touched

    return (
        <div className="space-y-2 mt-5">
            <label className={cn(
                "text-sm font-medium",
                hasError ? "text-red-600" : "text-muted-foreground"
            )}>
                Design Style *
            </label>
            <Select
                value={value}
                onValueChange={handleValueChange}
                onOpenChange={handleOpenChange}
            >
                <SelectTrigger
                    className={cn(
                        "w-full transition-colors",
                        hasError && [
                            "border-red-500 focus:border-red-500 focus:ring-red-500/20",
                            "bg-red-50 dark:bg-red-950/10"
                        ]
                    )}
                    aria-invalid={hasError}
                    aria-describedby={hasError ? "design-type-error" : undefined}
                >
                    <SelectValue placeholder="Choose a design style..." />
                </SelectTrigger>
        <SelectContent className="max-h-[300px]">
          <SelectItem value="Modern">Modern</SelectItem>
          <SelectItem value="Contemporary">Contemporary</SelectItem>
          <SelectItem value="Minimalist">Minimalist</SelectItem>
          <SelectItem value="Scandinavian">Scandinavian</SelectItem>
          <SelectItem value="Industrial">Industrial</SelectItem>
          <SelectItem value="Mid-century Modern">Mid-century Modern</SelectItem>
          <SelectItem value="Japandi">Japandi</SelectItem>
          <SelectItem value="Traditional">Traditional</SelectItem>
          <SelectItem value="Transitional">Transitional</SelectItem>
          <SelectItem value="Rustic">Rustic</SelectItem>
          <SelectItem value="Bohemian">Bohemian</SelectItem>
          <SelectItem value="Farmhouse">Farmhouse</SelectItem>
          <SelectItem value="Coastal">Coastal</SelectItem>
          <SelectItem value="Mediterranean">Mediterranean</SelectItem>
          <SelectItem value="French Country">French Country</SelectItem>
          <SelectItem value="Art Deco">Art Deco</SelectItem>
          <SelectItem value="Art Nouveau">Art Nouveau</SelectItem>
          <SelectItem value="Bauhaus">Bauhaus</SelectItem>
          <SelectItem value="Shabby Chic">Shabby Chic</SelectItem>
          <SelectItem value="Cottagecore">Cottagecore</SelectItem>
          <SelectItem value="Zen">Zen</SelectItem>
          <SelectItem value="Japanese Design">Japanese Design</SelectItem>
          <SelectItem value="Asian Décor">Asian Décor</SelectItem>
          <SelectItem value="Moroccan">Moroccan</SelectItem>
          <SelectItem value="Hollywood Regency">Hollywood Regency</SelectItem>
          <SelectItem value="Hollywood Glam">Hollywood Glam</SelectItem>
          <SelectItem value="Eclectic">Eclectic</SelectItem>
          <SelectItem value="Southwestern">Southwestern</SelectItem>
          <SelectItem value="Vintage">Vintage</SelectItem>
          <SelectItem value="Tropical">Tropical</SelectItem>
          <SelectItem value="Biophilic">Biophilic</SelectItem>
          <SelectItem value="Luxury">Luxury</SelectItem>
          <SelectItem value="Urban">Urban</SelectItem>
          <SelectItem value="Retro">Retro</SelectItem>
        </SelectContent>
            </Select>

            {/* Error Message */}
            {hasError && (
                <div
                    id="design-type-error"
                    className="flex items-center gap-2 text-sm text-red-600"
                    role="alert"
                    aria-live="polite"
                >
                    <AlertCircle className="h-4 w-4 flex-shrink-0" />
                    <span>{error}</span>
                </div>
            )}

            {/* Success Indicator */}
            {value && !hasError && touched && (
                <div className="flex items-center gap-2 text-sm text-primary">
                    <div className="h-4 w-4 rounded-full bg-primary/10 flex items-center justify-center">
                        <div className="h-2 w-2 rounded-full bg-primary"></div>
                    </div>
                    <span>Design type selected</span>
                </div>
            )}
        </div>
    )
})

export default DesignType