"use client"
import { UserDetailContext } from '@/app/_context/UserDetailContext'
import { Button } from '@/components/ui/button'
import { UserButton } from '@clerk/clerk-react'
import Image from 'next/image'
import Link from 'next/link'
import React, { useContext } from 'react'

import { SimpleThemeToggle } from '@/components/ui/theme-toggle'
import BrandLogo from '@/components/ui/brand-logo'

function Header() {
    const {userDetail}=useContext(UserDetailContext);
  return (
    <div className='p-5 shadow-sm flex justify-between items-center'>
        <Link href={'/'}>
            <BrandLogo size="default" />
        </Link>

        <Button variant="ghost" className="rounded-full text-primary" asChild>
            <Link href={'/dashboard/buy-credits'}>Get More Credits</Link>
        </Button>

       <div className='flex gap-4 items-center'>
           {userDetail?.credits&& <div className='flex gap-2 p-1 items-center bg-muted px-3 rounded-full'>
                <Image src={'/star.png'} alt='credits star icon' width={20} height={20}/>
                <span className="font-medium">{userDetail?.credits}</span>
            </div>}
            <SimpleThemeToggle />
            <UserButton/>
            <Button asChild>
                <Link href={'/dashboard'}>Dashboard</Link>
            </Button>
        </div>
       
  
       
    </div>
  )
}

export default Header