Stack trace:
Frame         Function      Args
0007FFFF7BB0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFF7BB0, 0007FFFF6AB0) msys-2.0.dll+0x2118E
0007FFFF7BB0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF7BB0  0002100469F2 (00021028DF99, 0007FFFF7A68, 0007FFFF7BB0, 000000000000) msys-2.0.dll+0x69F2
0007FFFF7BB0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF7BB0  00021006A545 (0007FFFF7BC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF7BC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFAC4F40000 ntdll.dll
7FFAC4210000 KERNEL32.DLL
7FFAC21E0000 KERNELBASE.dll
7FFAC4B90000 USER32.dll
7FFAC2690000 win32u.dll
000210040000 msys-2.0.dll
7FFAC4400000 GDI32.dll
7FFAC2BA0000 gdi32full.dll
7FFAC2AF0000 msvcp_win.dll
7FFAC2090000 ucrtbase.dll
7FFAC4DB0000 advapi32.dll
7FFAC4AE0000 msvcrt.dll
7FFAC4160000 sechost.dll
7FFAC42E0000 RPCRT4.dll
7FFAC1690000 CRYPTBASE.DLL
7FFAC2A50000 bcryptPrimitives.dll
7FFAC3E20000 IMM32.DLL
