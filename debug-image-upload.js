// Debug script to test image upload functionality
// Run this with: node debug-image-upload.js

const fs = require('fs');
const path = require('path');

// Function to analyze file properties
function analyzeFile(filePath) {
    try {
        const stats = fs.statSync(filePath);
        const buffer = fs.readFileSync(filePath);
        
        // Get file extension
        const ext = path.extname(filePath).toLowerCase();
        
        // Check file signature (magic bytes)
        let fileType = 'unknown';
        if (buffer.length >= 4) {
            const signature = buffer.slice(0, 4);
            
            // JPEG signatures
            if (signature[0] === 0xFF && signature[1] === 0xD8) {
                fileType = 'JPEG';
            }
            // PNG signature
            else if (signature[0] === 0x89 && signature[1] === 0x50 && 
                     signature[2] === 0x4E && signature[3] === 0x47) {
                fileType = 'PNG';
            }
        }
        
        console.log(`File Analysis for: ${filePath}`);
        console.log(`- Extension: ${ext}`);
        console.log(`- Detected Type: ${fileType}`);
        console.log(`- Size: ${stats.size} bytes`);
        console.log(`- First 10 bytes: ${Array.from(buffer.slice(0, 10)).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' ')}`);
        console.log('---');
        
        return {
            extension: ext,
            detectedType: fileType,
            size: stats.size,
            isValid: fileType !== 'unknown'
        };
    } catch (error) {
        console.error(`Error analyzing file ${filePath}:`, error.message);
        return null;
    }
}

// Test with sample files if they exist
const testFiles = [
    './test-images/sample.jpg',
    './test-images/sample.jpeg', 
    './test-images/sample.png'
];

console.log('=== Image File Analysis ===\n');

testFiles.forEach(file => {
    if (fs.existsSync(file)) {
        analyzeFile(file);
    } else {
        console.log(`File not found: ${file}`);
    }
});

console.log('\n=== Recommendations ===');
console.log('1. Ensure PNG files have correct magic bytes (89 50 4E 47)');
console.log('2. Check file size limits (should be reasonable for web upload)');
console.log('3. Verify MIME type detection in your upload handler');
console.log('4. Test with different PNG variants (8-bit, 24-bit, with/without alpha)');
