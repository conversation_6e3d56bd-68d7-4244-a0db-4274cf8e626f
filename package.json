{"name": "ai-room-redesign", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^5.7.5", "@magiclabs/ui": "^0.27.4", "@neondatabase/serverless": "^0.10.4", "@paypal/react-paypal-js": "^8.7.0", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@types/react-slick": "^0.23.13", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "firebase": "^11.7.3", "framer-motion": "^12.16.0", "lucide-react": "^0.453.0", "next": "^14.2.28", "react": "^18", "react-before-after-slider-component": "^1.1.8", "react-dom": "^18", "react-slick": "^0.30.3", "replicate": "^0.33.0", "resend": "^4.6.0", "slick-carousel": "^1.8.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"drizzle-kit": "^0.18.1", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.19.4"}}