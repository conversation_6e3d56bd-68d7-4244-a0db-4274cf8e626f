
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import "./globals.css";
import {Outfit} from 'next/font/google'
import Provider from "./provider";


export const metadata = {
  title: 'RoomDesignsAI | Transform Any Space with AI-Powered Interior Design',
  description: 'Transform your room in seconds with AI-powered design. Upload a photo and see endless interior design possibilities. Perfect for home makeovers, renovations, and design inspiration.',
  keywords: 'RoomDesignsAI, AI room design, virtual room makeover, interior design AI, home decor ideas, room planner, AI home design, virtual staging, room visualization',

};

const outfit=Outfit({subsets:['latin']})

export default function RootLayout({ children }) {
  return (
    <ClerkProvider>
    <html lang="en" className="dark">
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              try {
                const savedTheme = localStorage.getItem('theme');
                const prefersLight = window.matchMedia('(prefers-color-scheme: light)').matches;

                let theme = 'dark'; // Default to dark

                if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {
                  theme = savedTheme;
                } else if (prefersLight) {
                  theme = 'light';
                }

                document.documentElement.classList.remove('light', 'dark');
                document.documentElement.classList.add(theme);
              } catch (e) {
                document.documentElement.classList.add('dark');
              }
            `,
          }}
        />
      </head>
      <body
        className={outfit.className}
      >
        <Provider>
        {children}
        </Provider>

      </body>
    </html>
    </ClerkProvider>
  );
}
