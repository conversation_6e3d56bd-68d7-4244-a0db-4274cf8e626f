"use client"
import { But<PERSON> } from '@/components/ui/button';
import { useUser } from '@clerk/clerk-react'
import React, { useEffect, useState } from 'react'
import EmptyState from './EmptyState';
import Link from 'next/link';
import { db } from '@/config/db';
import { AiGeneratedImage } from '@/config/schema';
import { desc, eq } from 'drizzle-orm';
import RoomDesignCard from './RoomDesignCard';
import AiOutputDialog from './AiOutputDialog';
import { ChevronLeft, ChevronRight } from 'lucide-react';

function Listing() {
    const { user } = useUser();
    const [userRoomList, setUserRoomList] = useState([]);
    const [openDialog,setOpenDialog]=useState(false);
    const [selectedRoom,setSelectedRoom]=useState();
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(0);
    const itemsPerPage = 30;

    useEffect(()=>{
        user&&GetUserRoomList();
    },[user])

    const GetUserRoomList=async()=>{
        const result=await db.select().from(AiGeneratedImage)
        .where(eq(AiGeneratedImage.userEmail,user?.primaryEmailAddress?.emailAddress))
        .orderBy(desc( AiGeneratedImage.id))

        setUserRoomList(result);
        setTotalPages(Math.ceil(result.length / itemsPerPage));
        console.log(result);
    }

    // Get current page items
    const getCurrentPageItems = () => {
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        return userRoomList.slice(startIndex, endIndex);
    }

    // Handle page change
    const handlePageChange = (page) => {
        setCurrentPage(page);
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    // Generate page numbers for pagination
    const getPageNumbers = () => {
        const pages = [];
        const maxVisiblePages = 5;

        if (totalPages <= maxVisiblePages) {
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i);
            }
        } else {
            if (currentPage <= 3) {
                for (let i = 1; i <= 4; i++) {
                    pages.push(i);
                }
                pages.push('...');
                pages.push(totalPages);
            } else if (currentPage >= totalPages - 2) {
                pages.push(1);
                pages.push('...');
                for (let i = totalPages - 3; i <= totalPages; i++) {
                    pages.push(i);
                }
            } else {
                pages.push(1);
                pages.push('...');
                for (let i = currentPage - 1; i <= currentPage + 1; i++) {
                    pages.push(i);
                }
                pages.push('...');
                pages.push(totalPages);
            }
        }
        return pages;
    }
    return (
        <div>
            <div className='flex items-center justify-between'>
                <h2 className='font-bold text-3xl'>Hello, {user?.fullName}</h2>
                <Link href={'/dashboard/create-new'}>
                    <Button>+ Redesign Room</Button>
                </Link>
            </div>


            {userRoomList?.length == 0 ?
                <EmptyState />
                :
                <div className='mt-10'>
                    <div className='flex items-center justify-between mb-10'>
                        <h2 className='font-medium text-primary text-xl'>AI Room Studio</h2>
                        <div className='text-sm text-muted-foreground'>
                            Showing {getCurrentPageItems().length} of {userRoomList.length} designs
                        </div>
                    </div>

                    {/* Listing  */}
                    <div className='grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-10'>
                        {getCurrentPageItems().map((room,index)=>(
                            <div key={room.id} onClick={()=>{setOpenDialog(true);setSelectedRoom(room)}}>
                                <RoomDesignCard room={room}/>
                            </div>
                        ))}
                    </div>

                    {/* Pagination */}
                    {totalPages > 1 && (
                        <div className='flex items-center justify-center mt-12 space-x-2'>
                            {/* Previous Button */}
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handlePageChange(currentPage - 1)}
                                disabled={currentPage === 1}
                                className="flex items-center gap-1"
                            >
                                <ChevronLeft className="h-4 w-4" />
                                Previous
                            </Button>

                            {/* Page Numbers */}
                            <div className='flex items-center space-x-1'>
                                {getPageNumbers().map((page, index) => (
                                    page === '...' ? (
                                        <span key={index} className="px-2 py-1 text-muted-foreground">
                                            ...
                                        </span>
                                    ) : (
                                        <Button
                                            key={index}
                                            variant={currentPage === page ? "default" : "outline"}
                                            size="sm"
                                            onClick={() => handlePageChange(page)}
                                            className="min-w-[40px]"
                                        >
                                            {page}
                                        </Button>
                                    )
                                ))}
                            </div>

                            {/* Next Button */}
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handlePageChange(currentPage + 1)}
                                disabled={currentPage === totalPages}
                                className="flex items-center gap-1"
                            >
                                Next
                                <ChevronRight className="h-4 w-4" />
                            </Button>
                        </div>
                    )}
                </div>
            }
 <AiOutputDialog aiImage={selectedRoom?.aiImage} orgImage={selectedRoom?.orgImage}
        closeDialog={()=>setOpenDialog(false)}
        openDialog={openDialog}
        />
        </div>
    )
}

export default Listing