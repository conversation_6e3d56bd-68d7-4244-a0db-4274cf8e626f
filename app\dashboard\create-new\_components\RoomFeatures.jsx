import React, { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { ChevronDown, X, Check } from "lucide-react"

function RoomFeatures({ selectedFeatures }) {
  const [selectedOptions, setSelectedOptions] = useState([])
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef(null)

  const featureOptions = [
    "Large windows", "Bay window", "Floor-to-ceiling windows", "Skylight", "Fireplace",
    "Statement lighting", "Chandelier", "Pendant lights", "Recessed lighting", "Built-in shelves",
    "Open shelving", "Reading nook", "Window seat", "Breakfast bar", "Kitchen island",
    "Walk-in closet", "Built-in wardrobe", "Home bar", "Gallery wall", "Accent wall",
    "Feature wall", "Indoor plants", "Vertical garden", "Wall art", "Area rug",
    "Canopy bed", "Loft bed", "Daybed", "Murphy bed", "Freestanding bathtub",
    "Rain shower", "Double vanity", "Workspace", "Home office nook", "Built-in desk",
    "Media wall", "TV wall", "Entertainment center", "Smart home features", "Under-cabinet lighting",
    "Outdoor access", "Patio doors", "Balcony", "Terrace", "Courtyard",
    "Sliding doors", "Partition wall", "Divider", "Exposed beams", "Exposed brick",
    "Vaulted ceiling", "Drop ceiling", "Floating staircase", "Glass railing", "Library wall",
    "Game zone", "Kids play area", "Pet nook", "Laundry area", "Mudroom bench",
    "Shoe rack", "Mirror wall"
  ]

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleFeatureToggle = (feature) => {
    let updatedFeatures
    if (selectedOptions.includes(feature)) {
      updatedFeatures = selectedOptions.filter(item => item !== feature)
    } else {
      updatedFeatures = [...selectedOptions, feature]
    }

    setSelectedOptions(updatedFeatures)
    selectedFeatures(updatedFeatures)
  }

  const handleRemoveFeature = (featureToRemove, e) => {
    e.stopPropagation()
    const updatedFeatures = selectedOptions.filter(feature => feature !== featureToRemove)
    setSelectedOptions(updatedFeatures)
    selectedFeatures(updatedFeatures)
  }

  const clearAllFeatures = (e) => {
    e.stopPropagation()
    setSelectedOptions([])
    selectedFeatures([])
  }

  const getDisplayText = () => {
    if (selectedOptions.length === 0) {
      return "Select room features..."
    } else if (selectedOptions.length === 1) {
      return selectedOptions[0]
    } else if (selectedOptions.length <= 3) {
      return selectedOptions.join(", ")
    } else {
      return `${selectedOptions.slice(0, 2).join(", ")} +${selectedOptions.length - 2} more`
    }
  }

  return (
    <div className="space-y-2 mt-5" ref={dropdownRef}>
      <label className="text-sm font-medium text-muted-foreground">
        Room Features - Multiple selection
      </label>

      {/* Dropdown Trigger */}
      <div className="relative">
        <Button
          variant="outline"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full justify-between text-left font-normal h-10"
        >
          <span className="truncate">{getDisplayText()}</span>
          <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </Button>

        {/* Selected Items as Tags (when dropdown is closed) */}
        {selectedOptions.length > 0 && !isOpen && (
          <div className="flex flex-wrap gap-1 mt-2">
            {selectedOptions.slice(0, 5).map((feature, index) => (
              <span
                key={index}
                className="inline-flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary text-xs rounded-md border"
              >
                {feature}
                <X
                  className="h-3 w-3 cursor-pointer hover:text-destructive"
                  onClick={(e) => handleRemoveFeature(feature, e)}
                />
              </span>
            ))}
            {selectedOptions.length > 5 && (
              <span className="px-2 py-1 text-xs text-muted-foreground">
                +{selectedOptions.length - 5} more
              </span>
            )}
            {selectedOptions.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFeatures}
                className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground"
              >
                Clear All
              </Button>
            )}
          </div>
        )}

        {/* Dropdown Content */}
        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-popover border rounded-md shadow-lg max-h-60 overflow-y-auto">
            <div className="p-2">
              <div className="grid grid-cols-1 gap-1">
                {featureOptions.map((feature, index) => (
                  <div
                    key={index}
                    onClick={() => handleFeatureToggle(feature)}
                    className={`
                      flex items-center justify-between px-3 py-2 text-sm cursor-pointer rounded-sm
                      hover:bg-accent hover:text-accent-foreground
                      ${selectedOptions.includes(feature) ? 'bg-accent text-accent-foreground' : ''}
                    `}
                  >
                    <span>{feature}</span>
                    {selectedOptions.includes(feature) && (
                      <Check className="h-4 w-4 text-primary" />
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Helper Text */}
      <p className="text-xs text-muted-foreground">
        {selectedOptions.length === 0
          ? "Click to select multiple features for your room"
          : `${selectedOptions.length} feature${selectedOptions.length > 1 ? 's' : ''} selected`
        }
      </p>
    </div>
  )
}

export default RoomFeatures