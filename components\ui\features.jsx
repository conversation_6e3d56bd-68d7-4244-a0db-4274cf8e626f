"use client";
import Image from "next/image";
import { useRef, useEffect } from "react";
import Slider from "react-slick";
import { m, LazyMotion, domAnimation } from "framer-motion";
import { Heart, Share2, Maximize2, ChevronLeft, ChevronRight } from "lucide-react";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

// Simple fix for slick slider styles
const SlickFix = () => {
  useEffect(() => {
    if (typeof document !== 'undefined') {
      const style = document.createElement('style');
      style.innerHTML = `
        .slick-dots li button:before {
          color: hsl(var(--muted-foreground)) !important;
          opacity: 0.5;
        }
        .slick-dots li.slick-active button:before {
          color: hsl(var(--primary)) !important;
          opacity: 1;
        }
        .slick-prev:before, .slick-next:before {
          color: hsl(var(--primary));
        }
      `;
      document.head.appendChild(style);
      return () => document.head.removeChild(style);
    }
  }, []);
  
  return null;
};

const MotionDiv = m.div;

const SampleNextArrow = (props) => {
  const { className, style, onClick } = props;
  return (
    <div
      className={`${className} !flex items-center justify-center w-16 h-16 bg-background/80 rounded-full shadow-lg hover:bg-background transition-all duration-300`}
      style={{
        ...style,
        display: "flex !important",
        right: "-20px",
        zIndex: 10,
        opacity: 0.9,
        alignItems: "center",
        justifyContent: "center"
      }}
      onClick={onClick}
    >
      <ChevronRight className="text-primary h-10 w-10" />
    </div>
  );
};

const SamplePrevArrow = (props) => {
  const { className, style, onClick } = props;
  return (
    <div
      className={`${className} !flex items-center justify-center w-16 h-16 bg-background/80 rounded-full shadow-lg hover:bg-background transition-all duration-300`}
      style={{
        ...style,
        display: "flex !important",
        left: "-20px",
        zIndex: 10,
        opacity: 0.9,
        alignItems: "center",
        justifyContent: "center"
      }}
      onClick={onClick}
    >
      <ChevronLeft className="text-primary h-10 w-10" />
    </div>
  );
};

const Features = () => {
  const sliderRef = useRef(null);
  
  const settings = {
    dots: true,
    infinite: true,
    speed: 300,
    slidesToShow: 3,
    slidesToScroll: 1,
    nextArrow: <SampleNextArrow />,
    prevArrow: <SamplePrevArrow />,
    arrows: true,
    autoplay: true,
    autoplaySpeed: 3000,
    pauseOnHover: true,
    cssEase: 'ease-in-out',
    centerMode: false,
    centerPadding: '0',
    swipeToSlide: true,
    responsive: [
      {
        breakpoint: 1280,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
          arrows: true
        }
      },
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
          arrows: true
        }
      },
      {
        breakpoint: 640,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          arrows: false,
          dots: true
        }
      }
    ]
  };
  const roomDesigns = [
    { 
      id: 1, 
      title: "Living Room", 
      style: "Industrial", 
      likes: 128,
      views: 542
    },
    { 
      id: 2, 
      title: "Living Room", 
      style: "Mid Century", 
      likes: 95,
      views: 421
    },
    { 
      id: 3, 
      title: "Living Room", 
      style: "Traditional", 
      likes: 142,
      views: 687
    },
    { 
      id: 4, 
      title: "Living Room", 
      style: "Mediterranian", 
      likes: 87,
      views: 356
    },
    { 
      id: 5, 
      title: "Living Room", 
      style: "Zen", 
      likes: 112,
      views: 498
    },
    { 
      id: 6, 
      title: "Living Room", 
      style: "Asian Decor", 
      likes: 76,
      views: 312
    },
    { 
      id: 7, 
      title: "Living Room", 
      style: "Eclectic", 
      likes: 134,
      views: 523
    },
    { 
      id: 8, 
      title: "Living Room", 
      style: "Hollywood Glam", 
      likes: 198,
      views: 745
    },
	
	{ 
      id: 9, 
      title: "Living Room", 
      style: "Vintage", 
      likes: 198,
      views: 745
    },
	
	
	{ 
      id: 10, 
      title: "Living Room", 
      style: "Tropical", 
      likes: 198,
      views: 745
    },
	
	{ 
      id: 11, 
      title: "Living Room", 
      style: "Transitional", 
      likes: 198,
      views: 745
    },
	
	
	{ 
      id: 12, 
      title: "Living Room", 
      style: "Bohemian", 
      likes: 198,
      views: 745
    },
	
	
	{ 
      id: 13, 
      title: "Living Room", 
      style: "Rustic", 
      likes: 198,
      views: 745
    },
	
	
	{ 
      id: 14, 
      title: "Living Room", 
      style: "Contemporary", 
      likes: 198,
      views: 745
    },
	
	
	
	
	{ 
      id: 15, 
      title: "Living Room", 
      style: "Frence Country", 
      likes: 198,
      views: 745
    },
	
	
	{ 
      id: 16, 
      title: "Living Room", 
      style: "Modern", 
      likes: 198,
      views: 745
    },
	
	
	{ 
      id: 17, 
      title: "Living Room", 
      style: "Scandavian", 
      likes: 198,
      views: 745
    },
	
	
	{ 
      id: 18, 
      title: "Living Room", 
      style: "Coastal", 
      likes: 198,
      views: 745
    },
	
	
	{ 
      id: 19, 
      title: "Living Room", 
      style: "Japanese", 
      likes: 198,
      views: 745
    },
	
	
	{ 
      id: 20, 
      title: "Living Room", 
      style: "Moraccan", 
      likes: 198,
      views: 745
    },
	
	
	{ 
      id: 21, 
      title: "Living Room", 
      style: "Luxury", 
      likes: 198,
      views: 745
    },
	
	
	{ 
      id: 22, 
      title: "Living Room", 
      style: "Urban", 
      likes: 198,
      views: 745
    },
	
	
	{ 
      id: 23, 
      title: "Living Room", 
      style: "Farmhouse", 
      likes: 198,
      views: 745
    }
	
	

	
  ];

  return (
    <LazyMotion features={domAnimation}>
      <SlickFix />
      <section className="py-16 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto relative">
        {/* Custom arrow container */}
        <style jsx global>{`
          .slick-prev:before, .slick-next:before {
            display: none;
          }
          .slick-prev, .slick-next {
            width: 40px;
            height: 40px;
            z-index: 10;
          }
          .slick-prev {
            left: -50px;
          }
          .slick-next {
            right: -50px;
          }
          @media (max-width: 1024px) {
            .slick-prev {
              left: -30px;
            }
            .slick-next {
              right: -30px;
            }
          }
          @media (max-width: 768px) {
            .slick-prev, .slick-next {
              display: none !important;
            }
          }
        `}</style>
        <div className="text-center mb-12">
          <m.h2
            className="text-3xl font-bold text-foreground sm:text-4xl"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            Featured AI Room Designs
          </m.h2>
          <m.p
            className="mt-4 text-lg text-muted-foreground max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            Explore our collection of AI-generated room designs for inspiration
          </m.p>
        </div>

        <div className="relative px-4 sm:px-6 lg:px-8">
          <Slider ref={sliderRef} {...settings} className="pb-12">
            {roomDesigns.map((design) => (
              <div key={design.id} className="px-2">
                <MotionDiv
                  className="group relative overflow-hidden rounded-2xl bg-card shadow-lg hover:shadow-xl transition-shadow duration-300"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="relative aspect-[4/3] overflow-hidden">
                    <Image
                      src={`/sample-${design.id}.png`}
                      alt={design.title}
                      width={400}
                      height={300}
                      className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                      priority={roomDesigns.indexOf(design) < 4}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-between p-4">
                      <div className="flex justify-end space-x-2">
                        <button className="p-2 rounded-full bg-white/10 backdrop-blur-sm text-white hover:bg-white/20 transition-colors">
                          <Share2 className="h-4 w-4" />
                        </button>
                        <button className="p-2 rounded-full bg-white/10 backdrop-blur-sm text-white hover:bg-white/20 transition-colors">
                          <Maximize2 className="h-4 w-4" />
                        </button>
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-white font-medium text-lg">{design.title}</h3>
                          <p className="text-sm text-gray-200">{design.style} Style</p>
                        </div>
                        <button className="flex items-center space-x-1 text-white hover:text-rose-400 transition-colors">
                          <Heart className="h-5 w-5 fill-current" />
                          <span className="text-sm">{design.likes}</span>
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="p-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="font-medium text-card-foreground">{design.title}</h3>
                        <p className="text-sm text-muted-foreground">{design.style} Style</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="flex items-center text-sm text-muted-foreground">
                          <Heart className="h-4 w-4 mr-1 text-rose-400" />
                          {design.likes}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          • {design.views} views
                        </span>
                      </div>
                    </div>
                  </div>
                </MotionDiv>
              </div>
            ))}
          </Slider>
        </div>
        <div className="mt-12 text-center">
          
        </div>
      </section>
    </LazyMotion>
  );
};

export default Features;