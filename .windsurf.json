{"$schema": "https://json.schemastore.org/windsurf-config", "version": 1, "project": {"name": "AI Room Redesign", "framework": "Next.js 14 (App Router)", "styling": "Tailwind CSS with Radix UI", "database": "Drizzle ORM with Neon", "auth": "Clerk", "payments": "PayPal"}, "documentation": "https://nextjs.org/docs", "repository": "YOUR_REPOSITORY_URL", "rules": {"code-style": {"enforce-typescript": false, "enforce-prettier": true, "max-line-length": 100, "quote-style": "single", "semi": true, "trailing-comma": "es5", "import-order": ["react", "next", "@/components", "@/lib", "@/app", "^[a-z]"]}, "next-js": {"app-router": true, "server-components": true, "route-handlers": true, "metadata-export": true, "image-optimization": true}, "react": {"hooks": {"enforce-rules-of-hooks": true, "exhaustive-deps": "warn"}, "prefer-function-declarations": true, "jsx-wrap-multiline": true}, "styling": {"tailwind": {"config": "./tailwind.config.js", "css": "./app/globals.css", "dark-mode": "class", "important": false, "class-order": ["layout", "position", "display", "flex", "grid", "spacing", "sizing", "typography", "backgrounds", "borders", "effects", "transitions", "transforms", "interactivity", "accessibility"], "custom-classes": {"button-gradient": "bg-gradient-to-tl from-blue-600 to-violet-600 hover:from-violet-600 hover:to-blue-600", "banner-gradient": "bg-gradient-to-r from-[#1c1c1c]/95 via-[#1c1c1c] to-[#1c1c1c]/95"}}, "radix": {"themes": true, "components": ["alert-dialog", "select", "slot"]}}, "state-management": {"context-api": true, "location": "@/app/_context", "prefer-server-state": true}, "database": {"orm": "drizzle", "migrations": "./drizzle", "schema-location": "./db/schema.js"}, "authentication": {"provider": "clerk", "protected-routes": ["/dashboard"], "public-routes": ["/", "/api/webhooks"]}, "api": {"directory": "./app/api", "response-format": "json", "error-handling": true, "routes": {"redesign-room": {"method": "POST", "auth": "required", "rate-limit": true, "validation": "zod"}, "verify-user": {"method": "GET", "auth": "required", "cache": "no-store"}}, "middleware": {"cors": true, "compression": true, "body-parser": true}, "rate-limiting": true, "cors": {"enabled": true, "origins": ["*"]}}, "payments": {"provider": "paypal", "currency": "USD", "test-mode": true}, "components": {"directory": "./components", "naming": "PascalCase", "extension": ".jsx", "patterns": {"ui": {"path": "components/ui", "prefix": "", "props": "interface"}, "dashboard": {"path": "app/dashboard/_components", "prefix": "", "props": "interface"}}, "structure": {"imports": ["react", "next", "ui", "hooks", "utils", "types"], "exports": "default", "propsInterface": true}, "max-lines-per-file": 300, "require-proptypes": true, "require-jsdoc": true}, "performance": {"image-optimization": {"enabled": true, "quality": 75, "formats": ["webp", "avif"], "sizes": [640, 750, 828, 1080, 1200]}, "lazy-loading": {"images": true, "components": true}, "caching": {"static-assets": true, "api-responses": true, "revalidate": {"default": 3600, "paths": {"/api/redesign-room": 0, "/dashboard": 0}}}}, "testing": {"required-coverage": 80, "test-directory": ["__tests__", "**/*.test.js", "**/*.test.jsx"], "require-tests-for": ["components", "lib", "utils"], "testing-library": {"prefer": "@testing-library/react", "user-event": true}}, "security": {"require-env-vars": ["NEXT_PUBLIC_", "DATABASE_URL", "CLERK_", "NEXT_PUBLIC_CLERK_", "REPLICATE_API_TOKEN"], "forbid-secrets-in-code": true, "require-input-sanitization": true, "csp": {"enabled": true, "report-only": false}}, "git": {"enforce-semantic-commits": true, "require-pr": true, "max-pr-size": 400, "require-reviewers": 1}, "documentation": {"require-readme": true, "require-jsdoc": {"enabled": true, "level": "exported"}, "require-changelog": true, "api-documentation": {"enabled": true, "format": "swagger", "path": "/api-docs"}}, "ai-features": {"replicate": {"require-error-handling": true, "cache-results": true, "retry-strategy": {"attempts": 3, "backoff": "exponential"}, "rate-limiting": {"requests-per-minute": 10, "concurrent-requests": 2}}, "image-processing": {"max-size": "10MB", "allowed-formats": ["jpg", "jpeg", "png", "webp"], "optimize-before-upload": true}}, "error-handling": {"ai-generation": {"fallback-ui": true, "retry-button": true, "error-messages": {"rate-limit": "You've reached your generation limit. Please try again later.", "processing-failed": "Failed to process your image. Please try again.", "invalid-input": "Please provide a valid room image."}}, "payment": {"fallback-ui": true, "retry-options": true, "support-contact": true}}, "imports": {"ordering": ["react", "next", "^@/components", "^@/lib", "^@/styles", "^[a-z]"], "newline-between-groups": true}}, "overrides": [{"files": ["*.test.js", "*.test.jsx", "**/__tests__/**"], "rules": {"max-lines-per-file": 500, "require-jsdoc": false, "react/display-name": "off"}}, {"files": ["app/api/**"], "rules": {"security/require-input-sanitization": "error", "no-console": ["error", {"allow": ["warn", "error"]}]}}, {"files": ["components/ui/**"], "rules": {"react/prop-types": "off"}}], "editor": {"defaultFormatter": "esbenp.prettier-vscode", "formatOnSave": true, "codeActionsOnSave": {"source.fixAll.eslint": true}}, "recommended-extensions": ["bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "clinyong.vscode-css-modules"]}