// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import {getStorage} from 'firebase/storage'
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: "saas-e3963.firebaseapp.com",
  //databaseURL: "https://apps-e02e4-default-rtdb.firebaseio.com",
  projectId: "saas-e3963",
  storageBucket: "saas-e3963.firebasestorage.app",
  messagingSenderId: "1089537843231",
   appId: "1:1089537843231:web:ba880a5d7c3429672af52a",
  measurementId: "G-XT72E56186"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
export const storage=getStorage(app)


