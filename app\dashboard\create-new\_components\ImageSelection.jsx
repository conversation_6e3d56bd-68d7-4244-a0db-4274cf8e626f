"use client"
import Image from 'next/image'
import React, { useState, useImperativeHandle, forwardRef } from 'react'
import { cn } from "@/lib/utils"
import { AlertCircle } from "lucide-react"

const ImageSelection = forwardRef(function ImageSelection({ selectedImage, onValidationChange }, ref) {

    const [file,setFile]=useState();
    const [error, setError] = useState("")
    const [touched, setTouched] = useState(false)

    // Expose validation method to parent component
    useImperativeHandle(ref, () => ({
        validate: () => {
            const isValid = validateSelection(file)
            setTouched(true)
            return isValid
        },
        reset: () => {
            setFile(null)
            setError("")
            setTouched(false)
        },
        getValue: () => file
    }))

    const validateSelection = (currentFile) => {
        if (!currentFile) {
            setError("Please select an image of your room")
            onValidationChange?.(false)
            return false
        }
        setError("")
        onValidationChange?.(true)
        return true
    }

    const onFileSelected=(event)=>{
        const selectedFile = event.target.files[0]
        console.log('Selected file:', {
            name: selectedFile?.name,
            size: selectedFile?.size,
            type: selectedFile?.type,
            lastModified: selectedFile?.lastModified
        });

        setFile(selectedFile)
        setTouched(true)
        validateSelection(selectedFile)
        selectedImage(selectedFile)
    }

    const hasError = error && touched

  return (
    <div>
        <label className={cn(
            "text-sm font-medium",
            hasError ? "text-red-600" : "text-muted-foreground"
        )}>
            Select Image of your room *
        </label>
        <div className='mt-3'>
            <label htmlFor='upload-image'>
                <div className={cn(
                    "border rounded-xl border-dotted flex justify-center cursor-pointer hover:shadow-lg transition-colors",
                    file ? 'p-0 bg-white' : 'p-28',
                    hasError ? [
                        "border-red-500 bg-red-50 dark:bg-red-950/10"
                    ] : [
                        "border-primary bg-slate-200"
                    ]
                )}>
                   {!file? <Image src={'/imageupload.png'} width={70} height={70}/>
                   :<Image src={URL.createObjectURL(file)} width={300} height={300}
                   className='w-[300px] h-[300px] object-cover'
                   />}
                </div>
            </label>
            <input type="file" accept='image/png, image/jpeg'
            id="upload-image"
            style={{display:'none'}}
            onChange={onFileSelected}
            />
        </div>

        {/* Error Message */}
        {hasError && (
            <div
                className="flex items-center gap-2 text-sm text-red-600 mt-2"
                role="alert"
                aria-live="polite"
            >
                <AlertCircle className="h-4 w-4 flex-shrink-0" />
                <span>{error}</span>
            </div>
        )}

        {/* Success Indicator */}
        {file && !hasError && touched && (
            <div className="flex items-center gap-2 text-sm text-primary mt-2">
                <div className="h-4 w-4 rounded-full bg-primary/10 flex items-center justify-center">
                    <div className="h-2 w-2 rounded-full bg-primary"></div>
                </div>
                <span>Image selected</span>
            </div>
        )}

        {/* Handwritten Tip */}
        <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 rounded-xl border-2 border-dashed border-blue-200 dark:border-blue-800">
          <div className="flex items-start gap-3">
            <div className="text-2xl">💡</div>
            <div className="flex-1">
              <p
                className="handwriting text-lg font-medium text-blue-800 dark:text-blue-200 leading-relaxed"
                style={{
                  transform: "rotate(-0.5deg)",
                  textShadow: "1px 1px 2px rgba(0,0,0,0.1)"
                }}
              >
                <span className="text-xl">✨ Pro Tip:</span> Start simple! Try generating your first design with just{" "}
                <span className="font-bold underline decoration-wavy decoration-yellow-400">Room Type</span> and{" "}
                <span className="font-bold underline decoration-wavy decoration-accent">Design Style</span> selected.{" "}
                <br />
                <span className="text-base italic mt-1 inline-block" style={{ transform: "rotate(0.3deg)" }}>
                  You can always add mood & features later for more customization! 🎨
                </span>
              </p>
            </div>
          </div>
        </div>
    </div>
  )
})

export default ImageSelection