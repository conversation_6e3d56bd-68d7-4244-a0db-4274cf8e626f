"use client"
import React, { useState, useContext } from 'react'
import { Button } from '@/components/ui/button'
import { UserDetailContext } from '@/app/_context/UserDetailContext'
import { db } from '@/config/db'
import { Users } from '@/config/schema'
import { eq } from 'drizzle-orm'

export default function TestCreditsPage() {
    const { userDetail, setUserDetail } = useContext(UserDetailContext)
    const [loading, setLoading] = useState(false)
    const [logs, setLogs] = useState([])

    const addLog = (message, type = 'info') => {
        const timestamp = new Date().toLocaleTimeString()
        setLogs(prev => [...prev, { message, type, timestamp }])
        console.log(`[${type.toUpperCase()}] ${message}`)
    }

    const testCreditDeduction = async () => {
        if (!userDetail?.email) {
            addLog('❌ No user email found', 'error')
            return
        }

        setLoading(true)
        addLog('🔄 Testing credit deduction...')
        addLog(`Current user: ${userDetail.email}`)
        addLog(`Current credits: ${userDetail.credits}`)

        try {
            const result = await db.update(Users).set({
                credits: userDetail?.credits - 1
            }).where(eq(Users.email, userDetail.email))
            .returning({ id: Users.id, credits: Users.credits })

            addLog(`✅ Database update result: ${JSON.stringify(result)}`, 'success')

            if (result && result.length > 0) {
                setUserDetail(prev => ({
                    ...prev,
                    credits: userDetail?.credits - 1
                }))
                addLog(`✅ Credits updated successfully! New credits: ${userDetail.credits - 1}`, 'success')
            } else {
                addLog('❌ No rows updated', 'error')
            }
        } catch (error) {
            addLog(`❌ Error: ${error.message}`, 'error')
        } finally {
            setLoading(false)
        }
    }

    const testCreditAddition = async () => {
        if (!userDetail?.email) {
            addLog('❌ No user email found', 'error')
            return
        }

        setLoading(true)
        addLog('🔄 Testing credit addition...')
        addLog(`Current user: ${userDetail.email}`)
        addLog(`Current credits: ${userDetail.credits}`)

        try {
            const result = await db.update(Users).set({
                credits: userDetail?.credits + 5
            }).where(eq(Users.email, userDetail.email))
            .returning({ id: Users.id, credits: Users.credits })

            addLog(`✅ Database update result: ${JSON.stringify(result)}`, 'success')

            if (result && result.length > 0) {
                setUserDetail(prev => ({
                    ...prev,
                    credits: userDetail?.credits + 5
                }))
                addLog(`✅ Credits added successfully! New credits: ${userDetail.credits + 5}`, 'success')
            } else {
                addLog('❌ No rows updated', 'error')
            }
        } catch (error) {
            addLog(`❌ Error: ${error.message}`, 'error')
        } finally {
            setLoading(false)
        }
    }

    const checkAllUsers = async () => {
        setLoading(true)
        addLog('🔄 Checking all users...')

        try {
            const allUsers = await db.select().from(Users)
            addLog(`📊 Total users: ${allUsers.length}`)

            // Group by email to detect duplicates
            const emailGroups = {}
            allUsers.forEach((user) => {
                if (!emailGroups[user.email]) {
                    emailGroups[user.email] = []
                }
                emailGroups[user.email].push(user)
            })

            allUsers.forEach((user, index) => {
                const duplicateCount = emailGroups[user.email].length
                const duplicateInfo = duplicateCount > 1 ? ` (DUPLICATE - ${duplicateCount} entries)` : ''
                addLog(`User ${index + 1}: ${user.email} - ${user.credits} credits${duplicateInfo}`)
            })
        } catch (error) {
            addLog(`❌ Error: ${error.message}`, 'error')
        } finally {
            setLoading(false)
        }
    }

    const cleanupDuplicates = async () => {
        setLoading(true)
        addLog('🔄 Cleaning up duplicate users...')

        try {
            const response = await fetch('/api/debug-credits', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ action: 'cleanup-duplicates' })
            })

            const result = await response.json()

            if (result.success) {
                addLog(`✅ ${result.message}`, 'success')
                if (result.deletedIds) {
                    addLog(`Deleted user IDs: ${result.deletedIds.join(', ')}`, 'success')
                }
            } else {
                addLog(`❌ Error: ${result.error}`, 'error')
            }
        } catch (error) {
            addLog(`❌ Error: ${error.message}`, 'error')
        } finally {
            setLoading(false)
        }
    }

    const clearLogs = () => {
        setLogs([])
    }

    return (
        <div className="container mx-auto p-6 max-w-4xl">
            <h1 className="text-3xl font-bold mb-6">Credit System Test</h1>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Controls */}
                <div className="space-y-4">
                    <h2 className="text-xl font-semibold">Test Controls</h2>
                    
                    <div className="bg-gray-100 rounded-lg p-4">
                        <h3 className="font-medium mb-2">Current User Info</h3>
                        <p><strong>Email:</strong> {userDetail?.email || 'Not logged in'}</p>
                        <p><strong>Credits:</strong> {userDetail?.credits || 'N/A'}</p>
                    </div>
                    
                    <div className="space-y-2">
                        <Button 
                            onClick={testCreditDeduction} 
                            disabled={loading || !userDetail?.email}
                            className="w-full"
                            variant="destructive"
                        >
                            {loading ? 'Testing...' : 'Test Credit Deduction (-1)'}
                        </Button>
                        
                        <Button 
                            onClick={testCreditAddition} 
                            disabled={loading || !userDetail?.email}
                            className="w-full"
                        >
                            {loading ? 'Testing...' : 'Test Credit Addition (+5)'}
                        </Button>
                        
                        <Button
                            onClick={checkAllUsers}
                            disabled={loading}
                            className="w-full"
                            variant="outline"
                        >
                            {loading ? 'Checking...' : 'Check All Users'}
                        </Button>

                        <Button
                            onClick={cleanupDuplicates}
                            disabled={loading}
                            className="w-full"
                            variant="destructive"
                        >
                            {loading ? 'Cleaning...' : 'Remove Duplicate Users'}
                        </Button>
                    </div>
                </div>
                
                {/* Logs */}
                <div className="space-y-4">
                    <div className="flex justify-between items-center">
                        <h2 className="text-xl font-semibold">Test Logs</h2>
                        <Button onClick={clearLogs} variant="outline" size="sm">
                            Clear Logs
                        </Button>
                    </div>
                    
                    <div className="bg-gray-100 rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm">
                        {logs.length === 0 ? (
                            <p className="text-gray-500">No logs yet...</p>
                        ) : (
                            logs.map((log, index) => (
                                <div
                                    key={index}
                                    className={`mb-1 ${
                                        log.type === 'error' ? 'text-red-600' :
                                        log.type === 'success' ? 'text-green-600' :
                                        'text-gray-800'
                                    }`}
                                >
                                    <span className="text-gray-500">[{log.timestamp}]</span> {log.message}
                                </div>
                            ))
                        )}
                    </div>
                </div>
            </div>
        </div>
    )
}
