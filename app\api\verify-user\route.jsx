import { db } from "@/config/db";
import { Users } from "@/config/schema";
import { eq } from "drizzle-orm";
import { NextResponse } from "next/server";

export async function POST(req){
    const {user}=await req.json();

    try{
        console.log('Verifying user:', user?.primaryEmailAddress?.emailAddress);

        //If User Already Exist?
        const userInfo=await db.select().from(Users)
        .where(eq(Users.email,user?.primaryEmailAddress.emailAddress))
        console.log("User found in DB:",userInfo);

        //If Not Will Add New User to DB
        if(userInfo?.length==0)
        {
            console.log('Creating new user with default credits');
            const SaveResult=await db.insert(Users)
            .values({
                name:user?.fullName,
                email:user?.primaryEmailAddress.emailAddress,
                imageUrl:user?.imageUrl,
                credits: 3 // Explicitly set default credits
            }).returning({
                id: Users.id,
                name: Users.name,
                email: Users.email,
                imageUrl: Users.imageUrl,
                credits: Users.credits
            })

            console.log('New user created:', SaveResult[0]);
            return NextResponse.json({'result':SaveResult[0]})
        }

        console.log('Returning existing user:', userInfo[0]);
        return NextResponse.json({'result':userInfo[0]})
    }
    catch(e){
        console.error('Error in verify-user:', e);
        return NextResponse.json({error:e.message || 'Internal server error'}, {status: 500})
    }
}