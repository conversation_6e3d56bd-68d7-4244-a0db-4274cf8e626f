"use client"
import { createContext, useContext, useEffect, useState } from 'react'

const ThemeContext = createContext({
  theme: 'dark',
  setTheme: () => {},
  toggleTheme: () => {}
})

export function ThemeProvider({ children }) {
  const [theme, setTheme] = useState('dark')
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    // Check for saved theme preference or default to 'dark'
    try {
      const savedTheme = localStorage.getItem('theme')
      const prefersLight = window.matchMedia('(prefers-color-scheme: light)').matches
      const currentTheme = document.documentElement.classList.contains('dark') ? 'dark' : 'light'

      let newTheme = 'dark' // Default to dark

      if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {
        newTheme = savedTheme
      } else if (prefersLight) {
        newTheme = 'light'
      }

      // Only update state if different from current theme
      if (newTheme !== currentTheme) {
        setTheme(newTheme)
      } else {
        setTheme(currentTheme)
      }
    } catch (error) {
      console.log('Error accessing localStorage:', error)
      setTheme('dark')
    }
  }, [])

  useEffect(() => {
    if (!mounted) return

    try {
      // Apply theme to document
      const root = window.document.documentElement
      root.classList.remove('light', 'dark')
      root.classList.add(theme)

      // Save theme preference
      localStorage.setItem('theme', theme)
    } catch (error) {
      console.log('Error setting theme:', error)
    }
  }, [theme, mounted])

  const toggleTheme = () => {
    setTheme(prevTheme => prevTheme === 'light' ? 'dark' : 'light')
  }

  return (
    <ThemeContext.Provider value={{ theme, setTheme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  )
}

export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (!context) {
    // Return default values if context is not available
    return {
      theme: 'dark',
      setTheme: () => {},
      toggleTheme: () => {}
    }
  }
  return context
}
