import React, { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { ChevronDown, X, Check } from "lucide-react"

function Mood({ selectedMoods }) {
  const [selectedOptions, setSelectedOptions] = useState([])
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef(null)

  const moodOptions = [
    "Cozy", "Inviting", "Bright", "Calm", "Luxurious", "Warm", "Minimalist",
    "Playful", "Relaxing", "Elegant", "Vibrant", "Peaceful", "Fresh", "Artistic",
    "Energetic", "Sophisticated", "Rustic", "Airy", "Romantic", "Cheerful",
    "Tranquil", "Lively", "Retro", "Zen", "Moody", "Dramatic", "Serene",
    "Natural", "Soft", "Chic", "Whimsical", "Open", "Spacious", "Welcoming",
    "Classic", "Organic", "Festive", "Contemporary", "Refined", "Earthy",
    "Colorful", "Inspired"
  ]

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleMoodToggle = (mood) => {
    let updatedMoods
    if (selectedOptions.includes(mood)) {
      updatedMoods = selectedOptions.filter(item => item !== mood)
    } else {
      updatedMoods = [...selectedOptions, mood]
    }

    setSelectedOptions(updatedMoods)
    selectedMoods(updatedMoods)
  }

  const handleRemoveMood = (moodToRemove, e) => {
    e.stopPropagation()
    const updatedMoods = selectedOptions.filter(mood => mood !== moodToRemove)
    setSelectedOptions(updatedMoods)
    selectedMoods(updatedMoods)
  }

  const clearAllMoods = (e) => {
    e.stopPropagation()
    setSelectedOptions([])
    selectedMoods([])
  }

  const getDisplayText = () => {
    if (selectedOptions.length === 0) {
      return "Select mood(s)..."
    } else if (selectedOptions.length === 1) {
      return selectedOptions[0]
    } else if (selectedOptions.length <= 3) {
      return selectedOptions.join(", ")
    } else {
      return `${selectedOptions.slice(0, 2).join(", ")} +${selectedOptions.length - 2} more`
    }
  }

  return (
    <div className="space-y-2 mt-5" ref={dropdownRef}>
      <label className="text-sm font-medium text-muted-foreground">
        Mood(s) - Multiple selection
      </label>

      {/* Dropdown Trigger */}
      <div className="relative">
        <Button
          variant="outline"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full justify-between text-left font-normal h-10"
        >
          <span className="truncate">{getDisplayText()}</span>
          <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </Button>

        {/* Selected Items as Tags (when dropdown is closed) */}
        {selectedOptions.length > 0 && !isOpen && (
          <div className="flex flex-wrap gap-1 mt-2">
            {selectedOptions.slice(0, 5).map((mood, index) => (
              <span
                key={index}
                className="inline-flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary text-xs rounded-md border"
              >
                {mood}
                <X
                  className="h-3 w-3 cursor-pointer hover:text-destructive"
                  onClick={(e) => handleRemoveMood(mood, e)}
                />
              </span>
            ))}
            {selectedOptions.length > 5 && (
              <span className="px-2 py-1 text-xs text-muted-foreground">
                +{selectedOptions.length - 5} more
              </span>
            )}
            {selectedOptions.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllMoods}
                className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground"
              >
                Clear All
              </Button>
            )}
          </div>
        )}

        {/* Dropdown Content */}
        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-popover border rounded-md shadow-lg max-h-60 overflow-y-auto">
            <div className="p-2">
              <div className="grid grid-cols-1 gap-1">
                {moodOptions.map((mood, index) => (
                  <div
                    key={index}
                    onClick={() => handleMoodToggle(mood)}
                    className={`
                      flex items-center justify-between px-3 py-2 text-sm cursor-pointer rounded-sm
                      hover:bg-accent hover:text-accent-foreground
                      ${selectedOptions.includes(mood) ? 'bg-accent text-accent-foreground' : ''}
                    `}
                  >
                    <span>{mood}</span>
                    {selectedOptions.includes(mood) && (
                      <Check className="h-4 w-4 text-primary" />
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Helper Text */}
      <p className="text-xs text-muted-foreground">
        {selectedOptions.length === 0
          ? "Click to select multiple moods for your design"
          : `${selectedOptions.length} mood${selectedOptions.length > 1 ? 's' : ''} selected`
        }
      </p>
    </div>
  )
}

export default Mood